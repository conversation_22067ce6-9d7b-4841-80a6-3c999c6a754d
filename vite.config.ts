import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vitejs.dev/config/
export default defineConfig({
  base: './', // 设置为相对路径，以便在任何路径下部署
  plugins: [vue(), vueDevTools()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  server: {
    // 为开发服务器配置代理
    proxy: {
      // 将/api开头的请求代理到后端服务
      '/api': {
        target: 'http://49.235.191.145:8500', // 后端API地址
        changeOrigin: true, // 必须设置为true，以避免跨域问题
        rewrite: (path) => path.replace(/^\/api/, ''), // 将路径中的/api移除
      },
    },
  },
})
