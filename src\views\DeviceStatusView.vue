<template>
  <div class="container">
    <!-- 设备控制 -->
    <div class="card">
      <div class="card-header">
        <span>设备控制</span>
        <router-link to="/history" class="header-button">查看历史数据</router-link>
      </div>
      <div class="card-body">
        <div v-for="device in deviceControls" :key="device.id" class="device-control-item">
          <div class="device-header">
            <span class="device-label">{{ device.label }}</span>
            <div class="device-stats">
              <span class="device-running-status">
                运行状态:
                <strong :class="device.isRunning ? 'status-running' : 'status-stopped'">{{
                  device.isRunning ? '运行中' : '已停止'
                }}</strong>
              </span>
              <span class="device-current">
                实时电流: <strong>{{ device.current?.toFixed(3) ?? '--' }} A</strong>
              </span>
            </div>
          </div>
          <div class="knob-switch">
            <div class="knob-switch-option" :class="{ active: device.status === 'manual' }" data-state="manual">
              手动
            </div>
            <div class="knob-switch-option" :class="{ active: device.status === 'stop' }" data-state="stop">
              停止
            </div>
            <div class="knob-switch-option" :class="{ active: device.status === 'auto' }" data-state="auto">
              自动
            </div>
          </div>
        </div>
        <p class="non-clickable-note">
          注：以上为旋钮状态显示，如需操作请使用"DO直控"或"定时任务"功能。
        </p>
      </div>
    </div>

    <!-- 浮球状态 -->
    <div class="card">
      <div class="card-header">浮球状态</div>
      <div class="card-body">
        <div class="info-grid">
          <div v-for="item in floatSwitchStatus" :key="item.label" class="info-item">
            <div class="label">{{ item.label }}</div>
            <div class="value">
              {{ item.value }}<span v-if="item.unit" class="unit">{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 温湿度 -->
    <div class="card">
      <div class="card-header">温湿度</div>
      <div class="card-body">
        <div class="info-grid">
          <div v-for="item in temperatureHumidityStatus" :key="item.label" class="info-item">
            <div class="label">{{ item.label }}</div>
            <div class="value">
              {{ item.value }}<span v-if="item.unit" class="unit">{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 电能表 -->
    <div class="card">
      <div class="card-header">电能表</div>
      <div class="card-body">
        <ul class="power-details-list">
          <li v-for="item in powerMeterDetails" :key="item.label">
            <span class="label">{{ item.label }}</span>
            <span class="value">{{ item.value ?? '--' }} {{ item.unit }}</span>
          </li>
        </ul>
      </div>
    </div>

    <!-- DO 直控 -->
    <div class="card">
      <div class="card-header">DO 直控</div>
      <div class="card-body">
        <ul class="do-control-list">
          <li v-for="doItem in doControls" :key="doItem.name">
            <span class="label">{{ doItem.label }}</span>
            <div class="actions">
              <button @click="controlDo(doItem.name, 1)" class="btn btn-on">开启</button>
              <button @click="controlDo(doItem.name, 0)" class="btn btn-off">关闭</button>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <!-- 定时任务 -->
    <div class="card">
      <div class="card-header">定时任务</div>
      <div class="card-body">
        <!-- 任务类型切换 -->
        <div class="task-type-switcher">
          <button :class="{ active: taskType === 'single' }" @click="taskType = 'single'">
            单次任务
          </button>
          <button :class="{ active: taskType === 'cycle' }" @click="taskType = 'cycle'">
            循环任务
          </button>
          <button :class="{ active: taskType === 'sequence' }" @click="taskType = 'sequence'">
            顺序任务
          </button>
        </div>

        <!-- 单次任务 -->
        <div v-if="taskType === 'single'">
          <!-- 创建任务表单 -->
          <div class="task-scheduler-form">
            <h3 class="form-title">创建新任务</h3>
            <div class="form-group">
              <label for="do-select">选择DO:</label>
              <select id="do-select" v-model="newTask.do_name">
                <option v-for="doItem in doControls" :key="doItem.name" :value="doItem.name">
                  {{ doItem.label }}
                </option>
              </select>
            </div>
            <div class="form-group">
              <label for="action-select">选择操作:</label>
              <select id="action-select" v-model.number="newTask.value">
                <option :value="1">开启</option>
                <option :value="0">关闭</option>
              </select>
            </div>
            <div class="form-group">
              <label for="delay-input">延迟时间 (分钟):</label>
              <input id="delay-input" type="number" v-model.number="newTask.delay_minutes" min="1"
                placeholder="输入分钟数" />
            </div>
            <button @click="scheduleNewTask" class="btn-submit" :disabled="!isSingleTaskFormValid.valid">
              添加任务
            </button>
            <p v-if="!isSingleTaskFormValid.valid" class="form-error-message">
              {{ isSingleTaskFormValid.message }}
            </p>
          </div>
        </div>

        <!-- 循环任务 -->
        <div v-if="taskType === 'cycle'">
          <!-- 创建循环任务表单 -->
          <div class="task-scheduler-form">
            <h3 class="form-title">创建循环任务</h3>
            <div class="form-group">
              <label for="cycle-do-select">选择DO:</label>
              <select id="cycle-do-select" v-model="newCycleTask.do_name">
                <option v-for="doItem in doControls" :key="doItem.name" :value="doItem.name">
                  {{ doItem.label }}
                </option>
              </select>
            </div>
            <div class="form-group">
              <label for="on-minutes-input">开启时长 (分钟):</label>
              <input id="on-minutes-input" type="number" v-model.number="newCycleTask.on_minutes" min="1"
                placeholder="例如: 240" />
            </div>
            <div class="form-group">
              <label for="off-minutes-input">关闭时长 (分钟):</label>
              <input id="off-minutes-input" type="number" v-model.number="newCycleTask.off_minutes" min="1"
                placeholder="例如: 120" />
            </div>
            <button @click="scheduleNewCycleTask" class="btn-submit" :disabled="!isCycleTaskFormValid.valid">
              启动循环任务
            </button>
            <p v-if="!isCycleTaskFormValid.valid" class="form-error-message">
              {{ isCycleTaskFormValid.message }}
            </p>
          </div>
        </div>

        <!-- 顺序任务 -->
        <div v-if="taskType === 'sequence'">
          <div class="task-scheduler-form">
            <h3 class="form-title">创建顺序循环任务</h3>
            <p class="form-description">A运行指定分钟后关闭，并立即启动B，B运行完后重新启动A，无限循环。</p>

            <!-- 设备A -->
            <div class="form-group">
              <label for="sequence-do-a-select">设备A:</label>
              <select id="sequence-do-a-select" v-model="newSequenceTask.do_a_name">
                <option v-for="doItem in doControls" :key="doItem.name" :value="doItem.name">
                  {{ doItem.label }}
                </option>
              </select>
            </div>
            <div class="form-group">
              <label for="sequence-a-minutes-input">设备A运行时长 (分钟):</label>
              <input id="sequence-a-minutes-input" type="number" v-model.number="newSequenceTask.do_a_minutes"
                min="1" />
            </div>

            <!-- 设备B -->
            <div class="form-group">
              <label for="sequence-do-b-select">设备B:</label>
              <select id="sequence-do-b-select" v-model="newSequenceTask.do_b_name">
                <option v-for="doItem in doControls" :key="doItem.name" :value="doItem.name">
                  {{ doItem.label }}
                </option>
              </select>
            </div>
            <div class="form-group">
              <label for="sequence-b-minutes-input">设备B运行时长 (分钟):</label>
              <input id="sequence-b-minutes-input" type="number" v-model.number="newSequenceTask.do_b_minutes"
                min="1" />
            </div>

            <button @click="scheduleNewSequenceTask" class="btn-submit" :disabled="!isSequenceTaskFormValid.valid">
              启动顺序任务
            </button>
            <p v-if="!isSequenceTaskFormValid.valid" class="form-error-message">
              {{ isSequenceTaskFormValid.message }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 活动任务列表 -->
    <div class="card">
      <div class="card-header">当前活动任务</div>
      <div class="card-body">
        <div class="scheduled-tasks-list">
          <ul v-if="allActiveTasks.length > 0">
            <li v-for="task in allActiveTasks" :key="task.id">
              <span v-html="task.description"></span>
              <button @click="task.cancel" class="btn-cancel">取消</button>
            </li>
          </ul>
          <p v-else>当前没有活动任务。</p>
        </div>
      </div>
    </div>

    <!-- 设备信息 -->
    <div class="card">
      <div class="card-header">设备信息</div>
      <div class="card-body">
        <ul class="device-info-list">
          <li v-for="item in deviceInfo" :key="item.label">
            <span class="label">{{ item.label }}</span>
            <span class="value">{{ item.value ?? '--' }}</span>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, reactive } from 'vue'

// -- 新增设备映射 (与后端保持一致) --
const DO_TO_DEVICE_MAPPING: Record<string, keyof DeviceData> = {
  DO21: 'water_pump1',
  DO22: 'water_pump2',
  DO23: 'air_pump1',
  DO24: 'air_pump2'
}
// -- 映射结束 --

// Define interfaces for our data structures to avoid using 'any'
interface DeviceStatus {
  auto_status: number;
  stop_status: number;
  manual_status: number;
}

// 从API返回的完整设备数据结构
interface DeviceData {
  float_switches: { float1: number; float2?: number };
  water_pump1: DeviceStatus;
  water_pump2: DeviceStatus;
  air_pump1: DeviceStatus;
  air_pump2: DeviceStatus;
  device_info: {
    sn: string;
    fw_version?: string;
    imei?: string;
  };
  last_updated: string;
  dianliucaiji2: {
    curr1_ch1: number;
    curr1_ch2: number;
    curr1_ch3: number;
    curr1_ch4: number;
    curr2_ch1?: number;
    curr2_ch2?: number;
    curr2_ch3?: number;
    curr2_ch4?: number;
  };
  wenshidu: {
    humidity: number;
    temperature: number;
  };
  diannengbiao: {
    voltages: { Ua: number; Ub: number; Uc: number; Ubc?: number; Uca?: number };
    currents: { Ia: number; Ib: number; Ic: number };
    active_power: { total: number; phaseA?: number; phaseB?: number; phaseC?: number };
    reactive_power: { total: number; phaseA?: number; phaseB?: number; phaseC?: number };
    active_energy?: number;
  };
  timestamp: string;
  deviceSN?: string;
  DO21_status?: number;
  DO22_status?: number;
  DO23_status?: number;
  DO24_status?: number;
}

// 定义常量
const SN = '02801925060700002997' // 设备SN
const BASE_URL = 'http://49.235.191.145:8500' // API基础地址

// 使用 ref 创建响应式状态
const deviceData = ref<DeviceData | null>(null)

// DO控制和任务调度相关状态
const doControls = ref([
  { name: 'DO21', label: '水泵1' },
  { name: 'DO22', label: '水泵2' },
  { name: 'DO23', label: '气泵1' },
  { name: 'DO24', label: '气泵2' }
])
const scheduledTasks = ref<Record<string, { run_date: string; args: [string, string, number] }>>({})
const newTask = reactive({
  do_name: 'DO21',
  value: 1, // 1 for ON, 0 for OFF
  delay_minutes: 60
})
// 新增：循环任务相关状态
const taskType = ref('single') // 'single' or 'cycle' or 'sequence'
const cyclicTasks = ref<
  Record<string, { do_name: string; on_minutes: number; off_minutes: number }>
>({})
const newCycleTask = reactive({
  do_name: 'DO21',
  on_minutes: 240,
  off_minutes: 120
})
// 新增：顺序任务相关状态
const sequenceTasks = ref<
  Record<
    string,
    { do_a_name: string; do_a_minutes: number; do_b_name: string; do_b_minutes: number }
  >
>({})
const newSequenceTask = reactive({
  do_a_name: 'DO23', // 默认为气泵1
  do_a_minutes: 30,
  do_b_name: 'DO24', // 默认为气泵2
  do_b_minutes: 30
})

// -- 新增：表单验证计算属性 --
const isSingleTaskFormValid = computed(() => {
  const deviceKey = DO_TO_DEVICE_MAPPING[newTask.do_name]
  if (!deviceData.value || !deviceKey) {
    return { valid: false, message: '设备数据加载中...' }
  }
  const module = deviceData.value[deviceKey] as DeviceStatus | undefined
  if (module?.auto_status !== 1) {
    return { valid: false, message: '所选设备必须处于自动模式才能安排任务。' }
  }
  return { valid: true, message: '' }
})

const isCycleTaskFormValid = computed(() => {
  const deviceKey = DO_TO_DEVICE_MAPPING[newCycleTask.do_name]
  if (!deviceData.value || !deviceKey) {
    return { valid: false, message: '设备数据加载中...' }
  }
  const module = deviceData.value[deviceKey] as DeviceStatus | undefined
  if (module?.auto_status !== 1) {
    return { valid: false, message: '所选设备必须处于自动模式才能安排任务。' }
  }
  return { valid: true, message: '' }
})

const isSequenceTaskFormValid = computed(() => {
  const deviceAKey = DO_TO_DEVICE_MAPPING[newSequenceTask.do_a_name]
  const deviceBKey = DO_TO_DEVICE_MAPPING[newSequenceTask.do_b_name]

  if (newSequenceTask.do_a_name === newSequenceTask.do_b_name) {
    return { valid: false, message: '设备A和设备B不能是同一个设备。' }
  }

  if (!deviceData.value || !deviceAKey || !deviceBKey) {
    return { valid: false, message: '设备数据加载中...' }
  }

  const moduleA = deviceData.value[deviceAKey] as DeviceStatus | undefined
  const moduleB = deviceData.value[deviceBKey] as DeviceStatus | undefined

  if (moduleA?.auto_status !== 1 || moduleB?.auto_status !== 1) {
    return { valid: false, message: '两个所选设备都必须处于自动模式才能安排任务。' }
  }

  return { valid: true, message: '' }
})
// -- 验证计算属性结束 --

// onMounted 生命周期钩子中调用接口获取数据
onMounted(() => {
  fetchDeviceData()
  // 同时获取所有任务类型
  fetchScheduledTasks()
  fetchCyclicTasks()
  fetchSequenceTasks()
  // 定时刷新
  setInterval(() => {
    fetchScheduledTasks()
    fetchCyclicTasks()
    fetchSequenceTasks()
  }, 30000)
})

/**
 * 从API获取设备数据
 */
async function fetchDeviceData() {
  const apiUrl = `${BASE_URL}/data/${SN}`

  try {
    const response = await fetch(apiUrl)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    // 直接将获取到的数据赋值给deviceData
    deviceData.value = await response.json()
  } catch (error) {
    console.error('获取设备数据失败:', error)
  }
}

/**
 * 控制DO开关
 * @param doName - DO的名称, e.g., 'DO21'
 * @param value - 状态, 1 for ON, 0 for OFF
 */
async function controlDo(doName: string, value: number) {
  const apiUrl = `${BASE_URL}/control/${SN}`
  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ do_name: doName, value: value })
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.detail || `HTTP error! status: ${response.status}`)
    }
    alert(result.message) // Simple feedback
  } catch (error) {
    console.error(`控制 ${doName} 失败:`, error)
    alert(`控制 ${doName} 失败: ${error}`)
  }
}

/**
 * 获取计划任务列表
 */
async function fetchScheduledTasks() {
  const apiUrl = `${BASE_URL}/schedule/tasks`
  try {
    const response = await fetch(apiUrl)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    scheduledTasks.value = await response.json()
  } catch (error) {
    console.error('获取计划任务失败:', error)
  }
}

/**
 * 获取循环任务列表
 */
async function fetchCyclicTasks() {
  const apiUrl = `${BASE_URL}/schedule/cycles`
  try {
    const response = await fetch(apiUrl)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    cyclicTasks.value = await response.json()
  } catch (error) {
    console.error('获取循环任务失败:', error)
  }
}

/**
 * 获取顺序任务列表
 */
async function fetchSequenceTasks() {
  const apiUrl = `${BASE_URL}/schedule/sequences`
  try {
    const response = await fetch(apiUrl)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    sequenceTasks.value = await response.json()
  } catch (error) {
    console.error('获取顺序任务失败:', error)
  }
}

/**
 * 安排新的DO控制任务
 */
async function scheduleNewTask() {
  if (newTask.delay_minutes <= 0) {
    alert('延迟分钟数必须大于0')
    return
  }
  const apiUrl = `${BASE_URL}/schedule/task`
  const requestBody = {
    sn: SN,
    do_name: newTask.do_name,
    value: newTask.value,
    delay_minutes: newTask.delay_minutes
  }
  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.detail || `HTTP error! status: ${response.status}`)
    }
    alert(result.message)
    // 刷新任务列表
    fetchScheduledTasks()
  } catch (error) {
    console.error('安排任务失败:', error)
    alert(`安排任务失败: ${error}`)
  }
}

/**
 * 取消计划任务
 * @param jobId - 任务ID
 */
async function cancelScheduledTask(jobId: string) {
  const apiUrl = `${BASE_URL}/schedule/task/${jobId}`
  try {
    const response = await fetch(apiUrl, {
      method: 'DELETE'
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.detail || `HTTP error! status: ${response.status}`)
    }
    alert(result.message)
    // 刷新任务列表
    fetchScheduledTasks()
  } catch (error) {
    console.error(`取消任务 ${jobId} 失败:`, error)
    alert(`取消任务 ${jobId} 失败: ${error}`)
  }
}

/**
 * 安排新的循环任务
 */
async function scheduleNewCycleTask() {
  if (newCycleTask.on_minutes <= 0 || newCycleTask.off_minutes <= 0) {
    alert('开启和关闭时长都必须大于0')
    return
  }
  const apiUrl = `${BASE_URL}/schedule/cycle`
  const requestBody = {
    sn: SN,
    do_name: newCycleTask.do_name,
    on_minutes: newCycleTask.on_minutes,
    off_minutes: newCycleTask.off_minutes
  }
  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.detail || `HTTP error! status: ${response.status}`)
    }
    alert(result.message)
    fetchCyclicTasks() // 刷新列表
  } catch (error) {
    console.error('安排循环任务失败:', error)
    alert(`安排循环任务失败: ${error}`)
  }
}

/**
 * 取消循环任务
 * @param cycleId - 循环任务ID
 */
async function cancelCyclicTask(cycleId: string) {
  const apiUrl = `${BASE_URL}/schedule/cycle/${cycleId}`
  try {
    const response = await fetch(apiUrl, {
      method: 'DELETE'
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.detail || `HTTP error! status: ${response.status}`)
    }
    alert(result.message)
    fetchCyclicTasks() // 刷新列表
  } catch (error) {
    console.error(`取消循环任务 ${cycleId} 失败:`, error)
    alert(`取消循环任务 ${cycleId} 失败: ${error}`)
  }
}

/**
 * 安排新的顺序任务
 */
async function scheduleNewSequenceTask() {
  if (
    newSequenceTask.do_a_minutes <= 0 ||
    newSequenceTask.do_b_minutes <= 0 ||
    newSequenceTask.do_a_name === newSequenceTask.do_b_name
  ) {
    alert('时长必须大于0，且两个设备不能相同。')
    return
  }
  const apiUrl = `${BASE_URL}/schedule/sequence`
  const requestBody = {
    sn: SN,
    ...newSequenceTask
  }
  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.detail || `HTTP error! status: ${response.status}`)
    }
    alert(result.message)
    fetchSequenceTasks() // 刷新列表
  } catch (error) {
    console.error('安排顺序任务失败:', error)
    alert(`安排顺序任务失败: ${error}`)
  }
}

/**
 * 取消顺序任务
 * @param sequenceId - 顺序任务ID
 */
async function cancelSequenceTask(sequenceId: string) {
  const apiUrl = `${BASE_URL}/schedule/sequence/${sequenceId}`
  try {
    const response = await fetch(apiUrl, {
      method: 'DELETE'
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.detail || `HTTP error! status: ${response.status}`)
    }
    alert(result.message)
    fetchSequenceTasks() // 刷新列表
  } catch (error) {
    console.error(`取消顺序任务 ${sequenceId} 失败:`, error)
    alert(`取消顺序任务 ${sequenceId} 失败: ${error}`)
  }
}

/**
 * 将假定为UTC的时间字符串格式化为中国时区 (UTC+8) 的本地时间格式.
 * @param ts - 时间字符串, e.g., '2025-07-11 13:50:45' or ISO string '2025-07-11T13:50:45'
 */
function formatToChinaTime(ts: string | undefined | null): string {
  if (!ts) return '--';
  try {
    // 后端返回的时间字符串已经是中国时区，我们只需正确解析并格式化。
    // 将空格替换为'T'以兼容 'YYYY-MM-DD HH:MM:SS' 格式。
    const dateStr = ts.replace(' ', 'T');
    // `new Date()` 会将 ISO 格式的字符串（不带时区指示符）解析为本地时间。
    return new Date(dateStr).toLocaleString('zh-CN');
  } catch (e) {
    console.error(`格式化时间戳失败: '${ts}'`, e);
    return ts; // 格式化失败则返回原始字符串
  }
}

/**
 * 根据DO名称获取其标签
 * @param doName - DO的名称, e.g., 'DO21'
 * @returns 对应的标签, e.g., '水泵1'
 */
function getDoLabel(doName: string): string {
  const doItem = doControls.value.find((item) => item.name === doName)
  return doItem ? doItem.label : doName
}

/**
 * 获取旋钮开关的状态
 * @param deviceStatus - 设备状态对象
 * @returns 'auto', 'stop', 'manual', or null
 */
function getKnobStatus(deviceStatus?: DeviceStatus): 'auto' | 'stop' | 'manual' | null {
  if (!deviceStatus) return null
  if (deviceStatus.auto_status === 1) return 'auto'
  if (deviceStatus.stop_status === 1) return 'stop'
  if (deviceStatus.manual_status === 1) return 'manual'
  return null
}

// 使用 computed 属性来派生状态，使模板更简洁
const deviceControls = computed(() => {
  const data = deviceData.value
  const c = data?.dianliucaiji2
  return [
    {
      id: 'water_pump1',
      label: '水泵1',
      status: getKnobStatus(data?.water_pump1),
      current: c?.curr1_ch1,
      isRunning: data?.DO21_status === 1
    },
    {
      id: 'water_pump2',
      label: '水泵2',
      status: getKnobStatus(data?.water_pump2),
      current: c?.curr1_ch2,
      isRunning: data?.DO22_status === 1
    },
    {
      id: 'air_pump1',
      label: '气泵1',
      status: getKnobStatus(data?.air_pump1),
      current: c?.curr1_ch3,
      isRunning: data?.DO23_status === 1
    },
    {
      id: 'air_pump2',
      label: '气泵2',
      status: getKnobStatus(data?.air_pump2),
      current: c?.curr1_ch4,
      isRunning: data?.DO24_status === 1
    }
  ]
})

const temperatureHumidityStatus = computed(() => {
  const temp = deviceData.value?.wenshidu?.temperature
  const hum = deviceData.value?.wenshidu?.humidity
  return [
    { label: '温度', value: temp != null ? temp.toFixed(1) : '--', unit: '°C' },
    { label: '湿度', value: hum != null ? hum.toFixed(1) : '--', unit: '%' }
  ]
})

const floatSwitchStatus = computed(() => {
  const float1 = deviceData.value?.float_switches?.float1
  return [{ label: '状态', value: float1 == null ? '--' : float1 === 1 ? '激活' : '未激活', unit: '' }]
})

const powerMeterDetails = computed(() => {
  const p = deviceData.value?.diannengbiao
  return [
    { label: 'A相电流', value: p?.currents?.Ia?.toFixed(3), unit: 'A' },
    { label: 'B相电流', value: p?.currents?.Ib?.toFixed(3), unit: 'A' },
    { label: 'C相电流', value: p?.currents?.Ic?.toFixed(3), unit: 'A' },
    { label: '总有功功率', value: p?.active_power?.total?.toFixed(1), unit: 'W' },
    { label: '用电度数', value: p?.active_energy?.toFixed(2), unit: 'kWh' }
  ]
})

const allActiveTasks = computed(() => {
  const tasks: { id: string; description: string; cancel: () => void }[] = []

  // Add single tasks
  for (const jobId in scheduledTasks.value) {
    const task = scheduledTasks.value[jobId]
    tasks.push({
      id: `single-${jobId}`,
      description: `单次任务: <strong>${getDoLabel(task.args[1])}</strong> 将在 ${formatToChinaTime(
        task.run_date
      )} <strong>${task.args[2] === 1 ? '开启' : '关闭'}</strong>`,
      cancel: () => cancelScheduledTask(jobId)
    })
  }

  // Add cyclic tasks
  for (const cycleId in cyclicTasks.value) {
    const task = cyclicTasks.value[cycleId]
    tasks.push({
      id: `cycle-${cycleId}`,
      description: `循环任务: <strong>${getDoLabel(task.do_name)}</strong> - 开启 <strong>${task.on_minutes
        }</strong> 分钟, 关闭 <strong>${task.off_minutes}</strong> 分钟`,
      cancel: () => cancelCyclicTask(cycleId)
    })
  }

  // Add sequence tasks
  for (const sequenceId in sequenceTasks.value) {
    const task = sequenceTasks.value[sequenceId]
    tasks.push({
      id: `sequence-${sequenceId}`,
      description: `顺序任务: <strong>${getDoLabel(task.do_a_name)}</strong> (${task.do_a_minutes
        }分) → <strong>${getDoLabel(task.do_b_name)}</strong> (${task.do_b_minutes}分)`,
      cancel: () => cancelSequenceTask(sequenceId)
    })
  }

  return tasks
})

const deviceInfo = computed(() => {
  const info = deviceData.value?.device_info
  const lastUpdated = deviceData.value?.last_updated
  return [
    { label: 'SN', value: info?.sn },
    { label: '固件版本', value: info?.fw_version },
    { label: 'IMEI', value: info?.imei },
    { label: '数据时间戳', value: formatToChinaTime(lastUpdated) }
  ]
})
</script>

<style scoped>
/* General Theme & Layout */
.container {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  padding: 15px;
  background-color: #f4f7f6;
  /* A softer background color */
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    'Helvetica Neue',
    Arial,
    sans-serif;
}

.card {
  background-color: #ffffff;
  border-radius: 16px;
  /* More rounded corners */
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.06);
  /* Softer shadow */
  margin-bottom: 24px;
  overflow: hidden;
  border: none;
  /* Remove default border */
}

.card-header {
  background-color: #ffffff;
  padding: 18px 24px;
  font-size: 1.15rem;
  /* Slightly larger font */
  font-weight: 600;
  /* Bolder */
  color: #343a40;
  border-bottom: 1px solid #f0f0f0;
  /* Lighter border */
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-body {
  padding: 24px;
}

/* Info Grid (Temp/Humidity) */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 20px;
}

.info-item .label {
  font-size: 0.9rem;
  color: #6c757d;
  margin-bottom: 8px;
}

.info-item .value {
  font-size: 2rem;
  font-weight: 600;
  color: #212529;
}

.info-item .unit {
  font-size: 1rem;
  margin-left: 4px;
  color: #6c757d;
}

/* Device & Power Lists */
.device-info-list,
.power-details-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0 20px;
}

.device-info-list li,
.power-details-list li {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.device-info-list li:last-child:nth-child(odd),
.power-details-list li:last-child:nth-child(odd) {
  grid-column: span 2;
}

.device-info-list li:last-child,
.power-details-list li:last-child {
  border-bottom: none;
}

.device-info-list .label,
.power-details-list .label {
  color: #6c757d;
}

.device-info-list .value,
.power-details-list .value {
  font-weight: 500;
  color: #343a40;
}

.device-info-list {
  grid-template-columns: 1fr;
}

/* Device Control Item */
.device-control-item {
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.device-control-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.device-label {
  font-weight: 600;
  font-size: 1.1rem;
}

.device-stats {
  text-align: right;
}

.device-running-status,
.device-current {
  font-size: 0.9rem;
  color: #6c757d;
  display: block;
}

.status-running {
  color: #28a745;
  font-weight: 600;
}

.status-stopped {
  color: #dc3545;
  font-weight: 600;
}

.device-current strong {
  color: #343a40;
  font-weight: 600;
}

/* Knob Switch */
.knob-switch {
  display: flex;
  justify-content: space-around;
  background-color: #e9ecef;
  border-radius: 28px;
  /* Pill shape */
  padding: 5px;
  margin-top: 12px;
}

.knob-switch-option {
  flex: 1;
  padding: 10px;
  text-align: center;
  border-radius: 24px;
  /* Pill shape */
  font-weight: 600;
  cursor: default;
  transition: all 0.3s ease;
  color: #6c757d;
  font-size: 0.9rem;
}

.knob-switch-option.active {
  color: #fff;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.knob-switch-option.active[data-state='auto'] {
  background: linear-gradient(45deg, #0d6efd, #0a58ca);
}

.knob-switch-option.active[data-state='stop'] {
  background: linear-gradient(45deg, #6c757d, #5a6268);
}

.knob-switch-option.active[data-state='manual'] {
  background: linear-gradient(45deg, #198754, #146c43);
}

/* DO Control */
.do-control-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.do-control-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.do-control-list li:last-child {
  border-bottom: none;
}

.do-control-list .label {
  font-weight: 500;
}

.do-control-list .actions .btn {
  margin-left: 8px;
  padding: 8px 18px;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 600;
  color: #fff;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.do-control-list .actions .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-on {
  background: linear-gradient(45deg, #198754, #146c43);
}

.btn-off {
  background: linear-gradient(45deg, #dc3545, #b02a37);
}

/* Task Scheduler */
.task-type-switcher {
  display: flex;
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #dee2e6;
}

.task-type-switcher button {
  flex: 1;
  padding: 10px;
  border: none;
  background-color: #f8f9fa;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #343a40;
  transition: background-color 0.3s, color 0.3s;
}

.task-type-switcher button.active {
  background-color: #0d6efd;
  color: #ffffff;
}

.task-scheduler-form {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.form-title,
.list-title {
  font-size: 16px;
  margin-top: 0;
  margin-bottom: 16px;
  font-weight: bold;
}

.form-group {
  margin-bottom: 12px;
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 6px;
  font-size: 14px;
  color: #6c757d;
}

.form-group select,
.form-group input {
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #ced4da;
  font-size: 1rem;
  background-color: #f8f9fa;
  transition:
    border-color 0.2s,
    box-shadow 0.2s;
}

.form-group select:focus,
.form-group input:focus {
  outline: none;
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.btn-submit {
  width: 100%;
  padding: 12px;
  background: linear-gradient(45deg, #0d6efd, #0a58ca);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-submit:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(13, 110, 253, 0.3);
}

.btn-submit:disabled {
  background: #adb5bd;
  cursor: not-allowed;
}

/* Tasks List */
.scheduled-tasks-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.scheduled-tasks-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 0.95rem;
}

.scheduled-tasks-list li:last-child {
  border-bottom: none;
}

.btn-cancel {
  padding: 5px 10px;
  font-size: 0.8rem;
  background: linear-gradient(45deg, #ffc107, #d39e00);
  color: #000;
  border: none;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-cancel:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

/* Utility */
.header-button {
  padding: 8px 16px;
  font-size: 0.9rem;
  font-weight: 600;
  background-color: #e9ecef;
  color: #0d6efd;
  border: none;
  text-decoration: none;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.header-button:hover {
  background-color: #dee2e6;
  transform: scale(1.03);
}

.form-description {
  font-size: 13px;
  color: #6c757d;
  margin-top: -10px;
  margin-bottom: 16px;
}

.form-error-message {
  font-size: 0.85rem;
  color: #dc3545;
  margin-top: 10px;
  text-align: center;
}

.non-clickable-note {
  font-size: 0.85rem;
  color: #6c757d;
  text-align: center;
  margin-top: 24px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 6px;
}
</style>
