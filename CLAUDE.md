# CLAUDE.md

始终使用中文和我交流

## Project Overview

This is a Vue 3 H5 application for monitoring and controlling water pumps and air pumps in a water utility station (水利站). The app provides real-time device status monitoring, manual control, and scheduled task management.

## Key Architecture

- **Framework**: Vue 3 with TypeScript, using Composition API
- **Build Tool**: Vite
- **State Management**: Pinia for local state, no complex global state needed
- **Router**: Vue Router with hash history for device compatibility
- **UI**: Custom CSS with card-based layout optimized for mobile devices

## Core Components

- `DeviceStatusView.vue`: Main dashboard with device controls, status monitoring, and task scheduling
- `HistoryView.vue`: Historical data view
- Router configured for two main views: device status (home) and history

## Device Control System

The application controls 4 devices via DO (Digital Output) signals:

- DO21: 水泵1 (Water Pump 1)
- DO22: 水泵2 (Water Pump 2)
- DO23: 气泵1 (Air Pump 1)
- DO24: 气泵2 (Air Pump 2)

Each device has three states controlled by physical knob switches:

- `auto_status`: Automatic mode
- `stop_status`: Stop mode
- `manual_status`: Manual mode

## API Integration

Base URL: `http://**************:8500`
Device SN: `02801925060700002997`

Key endpoints:

- `GET /data/{SN}`: Fetch device data and sensor readings
- `POST /control/{SN}`: Control DO switches
- `POST /schedule/task`: Schedule single tasks
- `POST /schedule/cycle`: Create cyclic tasks
- `POST /schedule/sequence`: Create sequence tasks

## Data Structure

Main device data includes:

- Device control states (auto/stop/manual for each pump)
- Real-time current measurements (dianliucaiji2)
- Temperature/humidity sensors (wenshidu)
- Float switch status (float_switches)
- Power meter readings (diannengbiao)
- DO status indicators

## Development Commands

```bash
# Install dependencies
npm install

# Development server
npm run dev

# Build for production  
npm run build

# Type checking
npm run type-check

# Linting
npm run lint

# Unit tests
npm run test:unit

# Preview production build
npm run preview

# Format code
npm run format
```

## Testing

- Uses Vitest for unit testing
- Test files should follow `*.test.ts` or `*.spec.ts` patterns
- Configuration in `vitest.config.ts`

## Code Conventions

- TypeScript strict mode enabled
- ESLint with Vue and TypeScript rules
- Prettier for code formatting
- Use Composition API over Options API
- Interface definitions for all API data structures
- Chinese comments and labels for UI elements (this is a Chinese application)

## Key Implementation Notes

- Real-time data fetching every 30 seconds
- Form validation prevents task creation when devices not in auto mode
- Time formatting assumes backend returns China timezone timestamps
- Mobile-optimized responsive design with card layout
- Task scheduling supports three types: single, cyclic, and sequence tasks
