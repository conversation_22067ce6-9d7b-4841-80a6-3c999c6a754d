import { createRouter, createWebHashHistory } from 'vue-router'
import DeviceStatusView from '../views/DeviceStatusView.vue'
import HistoryView from '../views/HistoryView.vue'

const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      name: 'home',
      component: DeviceStatusView,
    },
    {
      path: '/history',
      name: 'History',
      component: HistoryView,
    },
  ],
})

export default router
